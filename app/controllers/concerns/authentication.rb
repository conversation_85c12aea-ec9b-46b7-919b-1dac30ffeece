module Authentication
  extend ActiveSupport::Concern

  included do
    before_action :require_authentication
    helper_method :authenticated?
  end

  class_methods do
    def allow_unauthenticated_access(**options)
      skip_before_action :require_authentication, **options
    end
  end

  private
    def authenticated?
      result = resume_session
      Rails.logger.debug("Authentication: authenticated? returning #{result.present?}")
      result
    end

    def require_authentication
      result = resume_session || request_authentication
      Rails.logger.debug("Authentication: require_authentication completed")
      result
    end

    def resume_session
      Rails.logger.debug("Authentication: resume_session called, Current.session: #{Current.session.present?}")
      Current.session ||= find_session_by_cookie
      Rails.logger.debug("Authentication: resume_session result: #{Current.session.present?}")
      Current.session
    end

    def find_session_by_cookie
      session_id = cookies.signed[:session_id]
      Rails.logger.debug("Authentication: find_session_by_cookie with session_id: #{session_id.present? ? 'present' : 'nil'}")

      if session_id
        session = Session.find_by(id: session_id)
        Rails.logger.debug("Authentication: Found session: #{session.present?}")
        session
      else
        Rails.logger.debug("Authentication: No session cookie found")
        nil
      end
    end

    def request_authentication
      Rails.logger.debug("Authentication: request_authentication called, storing return_to: #{request.url}")
      session[:return_to_after_authenticating] = request.url
      redirect_to login_path
    end

    def after_authentication_url
      return_to = session.delete(:return_to_after_authenticating) || root_url
      Rails.logger.debug("Authentication: after_authentication_url returning: #{return_to}")
      return_to
    end

    def start_new_session_for(user)
      Rails.logger.debug("Authentication: start_new_session_for user: #{user.id}")
      user.sessions.create!(user_agent: request.user_agent, ip_address: request.remote_ip).tap do |session|
        Current.session = session
        cookies.signed.permanent[:session_id] = { value: session.id, httponly: true, same_site: :lax }
        Rails.logger.debug("Authentication: New session created with id: #{session.id}")
      end
    end

    def terminate_session
      Rails.logger.debug("Authentication: terminate_session called")
      Current.session.destroy
      cookies.delete(:session_id)
      Rails.logger.debug("Authentication: Session terminated")
    end
end
