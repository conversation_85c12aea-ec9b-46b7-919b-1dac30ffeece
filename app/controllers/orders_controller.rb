class OrdersController < ApplicationController
  include RedirectHelper

  # implicitly requires authentication

  def create
    @plan = Plan.find(params[:plan_id])

    # Only Pro users can purchase credit packages
    if @plan.credit_package? && !Current.user.pro?
      flash[:alert] = "Credit packages are available exclusively to Pro members. Upgrade your subscription to access them."
      return redirect_to(plans_path)
    end

    @order = Current.user.orders.build(
      plan: @plan,
      amount: @plan.unit_amount,
      currency: @plan.currency
    )

    checkout_session = @order.create_checkout_session!(
      success_url: success_orders_url,
      cancel_url: cancel_orders_url
    )

    # Get checkout URL from Stripe session
    checkout_url = checkout_session.url

    # Validate and redirect using Rails' recommended pattern
    if valid_stripe_checkout_url?(checkout_url)
      # Use url_from with fallback as recommended by Rails security guides
      redirect_to(
        url_from(checkout_url) || fallback_checkout_url(checkout_url),
        allow_other_host: true
      )
    else
      Rails.logger.error("Invalid checkout URL detected: #{checkout_url}")
      flash[:alert] = "Invalid checkout session. Please try again."
      redirect_to plans_path
    end
  rescue Stripe::StripeError => e
    Rails.logger.error("Stripe error: #{e.message}")
    flash[:alert] = "Payment processing error: #{e.message}"
    redirect_to plans_path
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error("Order creation failed: #{e.message}")
    flash[:alert] = "Order creation failed: #{e.message}"
    redirect_to plans_path
  end

  def success
    Rails.logger.debug("OrdersController#success: Payment success flow starting")

    # Check for stored redirect target from payment flow
    redirect_target = get_and_clear_redirect_target
    Rails.logger.debug("OrdersController: Retrieved redirect target: #{redirect_target.inspect}")

    if redirect_target.present?
      Rails.logger.info("OrdersController: Found redirect target, using redirect_with_flash")
      redirect_with_flash(
        redirect_target,
        profile_path,
        :notice,
        "Payment successful! Your credits have been added."
      )
    else
      Rails.logger.info("OrdersController: No redirect target found, using default redirect")
      flash[:notice] = "Payment successful! Your order is being processed."
      redirect_to profile_path
    end
  end

  def cancel
    flash[:alert] = "Payment was canceled. No charges were made."
    redirect_to plans_path
  end

  private

  def valid_stripe_checkout_url?(url)
    return false if url.blank?

    begin
      uri = URI.parse(url)

      # Only allow HTTPS Stripe checkout URLs
      return false unless uri.scheme == "https"
      return false unless uri.host == "checkout.stripe.com"

      true
    rescue URI::InvalidURIError
      false
    end
  end

  def fallback_checkout_url(original_url)
    # If url_from returns false (different domain), but we've validated it's a safe Stripe URL,
    # simply return the validated Stripe URL so the caller can handle the redirect.
    original_url
  end
end
