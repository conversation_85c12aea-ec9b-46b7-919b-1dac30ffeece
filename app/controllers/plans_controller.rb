class PlansController < ApplicationController
  include RedirectHelper

  allow_unauthenticated_access only: [ :index ]

  def index
    Rails.logger.debug("PlansController#index: Starting with params: #{params.inspect}")
    @subscription_plans = Plan.active.subscription_plans.order(:unit_amount)
    @credit_packages = Plan.active.credit_packages.order(:unit_amount)

    # Handle next parameter for post-payment redirect
    handle_next_parameter
  end

  private

  def handle_next_parameter
    next_path = params[:next]
    Rails.logger.debug("PlansController: handle_next_parameter called with next_path: #{next_path.inspect}")

    return unless next_path.present?
    Rails.logger.debug("PlansController: next_path is present, proceeding with handling")

    if authenticated?
      Rails.logger.debug("PlansController: User is authenticated, storing redirect target")
      # Store the redirect target for authenticated users
      result = store_redirect_target(next_path)
      Rails.logger.debug("PlansController: store_redirect_target returned: #{result}")
    else
      Rails.logger.debug("PlansController: User is not authenticated")
      # For unauthenticated users, we'll handle this after they authenticate
      # The authentication system will handle the redirect
      Rails.logger.info("PlansController: Next parameter received for unauthenticated user: #{next_path}")
    end
  end
end
