class SongsController < ApplicationController
  before_action :set_song, only: [ :show, :edit, :update, :toggle_favorite, :destroy ]

  def index
    # Get base songs query for the current user
    @songs = Current.user.songs
                    .joins(:generation_task)
                    .includes(:generation_task)

    # Apply search if present
    @songs = @songs.search(params[:search]) if params[:search].present?

    # Apply filters
    @songs = apply_filters(@songs)

    # Apply sorting
    @songs = apply_sorting(@songs)

    # Apply pagination
    @page = [ params[:page].to_i, 1 ].max
    @per_page = 10
    @total_count = @songs.count
    @total_pages = (@total_count.to_f / @per_page).ceil
    @songs = @songs.offset((@page - 1) * @per_page).limit(@per_page)

    # Get generation tasks that don't have songs yet (placeholders for generating tasks)
    # Only show on first page to avoid confusion
    @generating_tasks = @page == 1 ? Current.user.generation_tasks
                                            .queued
                                            .order(created_at: :desc) : []

    # Get failed generation tasks (to show failed items)
    # Only show on first page to avoid confusion
    @failed_tasks = @page == 1 ? Current.user.generation_tasks
                                        .failed
                                        .order(created_at: :desc) : []
  end

  def show
    # Song is set by before_action
  end

  def edit
    # Song is set by before_action
    # Return edit form in modal via Turbo Frame
  end

  def update
    if @song.update_title(song_params[:title])
      respond_to do |format|
        format.html do
          redirect_to @song, notice: "Song title updated successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "Song title updated successfully!" }
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def toggle_favorite
    if @song.toggle_favorite
      respond_to do |format|
        format.html do
          redirect_to @song, notice: @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!"
        end
        format.turbo_stream { flash.now[:notice] = @song.favorite? ? "#{@song.title} added to favorites!" : "#{@song.title} removed from favorites!" }
      end
    else
      render :show, status: :unprocessable_entity
    end
  end

  def destroy
    if @song.destroy
      respond_to do |format|
        format.html do
          redirect_to songs_path, notice: "#{@song.title} deleted successfully!"
        end
        format.turbo_stream { flash.now[:notice] = "#{@song.title} deleted successfully!" }
      end
    else
      redirect_to songs_path, alert: "Unable to delete song."
    end
  end

  private

  def set_song
    @song = Current.user.songs.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to songs_path, alert: "Song not found."
  end

  def song_params
    params.require(:song).permit(:title)
  end

  def apply_filters(songs)
    # Filter by favorites
    if params[:favorites] == "true"
      songs = songs.filter_by_favorite(true)
    end

    # Filter by genre
    if params[:genre].present?
      songs = songs.filter_by_genre(params[:genre])
    end

    # Filter by style
    if params[:style].present?
      songs = songs.filter_by_style(params[:style])
    end

    songs
  end

  def apply_sorting(songs)
    sort_field = params[:sort] || "default"
    sort_direction = params[:order] || "desc"

    case sort_field
    when "created_at"
      songs.sort_by_created_at(sort_direction)
    when "title"
      songs.sort_by_title(sort_direction)
    when "favorite"
      songs.sort_by_favorite(sort_direction)
    else
      # Default sorting: use generation priority
      songs.merge(GenerationTask.order_by_generation_priority)
    end
  end
end
