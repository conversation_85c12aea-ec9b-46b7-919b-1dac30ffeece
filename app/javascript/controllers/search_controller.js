import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="search"
export default class extends Controller {
  static targets = ["form", "input"]
  
  connect() {
    this.timeout = null
  }

  search() {
    // Clear existing timeout
    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    // Set new timeout for debounced search
    this.timeout = setTimeout(() => {
      this.submitSearch()
    }, 300) // 300ms debounce
  }

  submitSearch() {
    // Reset page to 1 when searching
    const pageInput = this.formTarget.querySelector('input[name="page"]')
    if (pageInput) {
      pageInput.value = 1
    }

    // Submit the form
    this.formTarget.requestSubmit()
  }

  disconnect() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  }
}
