class StripeWebhookService
  def initialize(event)
    @event = event
  end

  def process
    Rails.logger.info("🔍 Processing webhook event type: #{@event.type} with ID: #{@event.id}")

    # Check for idempotency - if event already processed, return cached result
    existing_event = StripeWebhookEvent.find_by(stripe_event_id: @event.id)

    if existing_event&.processed?
      Rails.logger.info("🔄 Event #{@event.id} already processed, returning cached result")
      return cached_result(existing_event)
    end

    # Process new event with idempotency protection
    process_with_idempotency
  rescue => e
    Rails.logger.error("💥 Error processing webhook #{@event.type}: #{e.message}")
    Rails.logger.error(e.backtrace.join("\n"))

    # Record the error if we have a webhook event record
    if @webhook_event
      @webhook_event.update!(
        processed_at: Time.current,
        processing_result: "failed",
        error_message: e.message
      )
    end

    { success: false, error: e.message }
  end

  private

  def process_with_idempotency
    result = nil

    StripeWebhookEvent.transaction do
      # Create or find webhook event record
      @webhook_event = StripeWebhookEvent.find_or_create_by!(
        stripe_event_id: @event.id
      ) do |event|
        event.event_type = @event.type
        event.processing_result = "processing"
      end

      # If already processed by another request, return cached result
      if @webhook_event.processed?
        return cached_result(@webhook_event)
      end

      # If stuck in processing for too long, reset and retry
      if @webhook_event.processing? && @webhook_event.created_at < 5.minutes.ago
        Rails.logger.warn("⚠️ Event #{@event.id} stuck in processing, resetting...")
        @webhook_event.update!(
          processing_result: "failed",
          error_message: "Processing timeout - resetting for retry"
        )
        # Create new record for retry
        @webhook_event = StripeWebhookEvent.create!(
          stripe_event_id: @event.id,
          event_type: @event.type,
          processing_result: "processing"
        )
      end

      # Execute actual event processing
      result = execute_event_processing

      # Update result
      @webhook_event.update!(
        processed_at: Time.current,
        processing_result: result[:success] ? "success" : "failed",
        error_message: result[:error]
      )
    end

    result
  rescue ActiveRecord::RecordNotUnique
    # Handle concurrent creation - find existing record and return result
    existing = StripeWebhookEvent.find_by(stripe_event_id: @event.id)
    cached_result(existing) if existing
  end

  def cached_result(webhook_event)
    if webhook_event.success?
      { success: true }
    else
      { success: false, error: webhook_event.error_message || "Processing failed" }
    end
  end

  def execute_event_processing
    case @event.type
    when "checkout.session.completed"
      Rails.logger.info("📦 Processing checkout.session.completed")
      handle_checkout_session_completed
    when "checkout.session.async_payment_succeeded"
      Rails.logger.info("📦 Processing checkout.session.async_payment_succeeded")
      handle_checkout_session_async_payment_succeeded
    when "checkout.session.expired"
      Rails.logger.info("📦 Processing checkout.session.expired")
      handle_checkout_session_expired
    when "checkout.session.async_payment_failed"
      Rails.logger.info("📦 Processing checkout.session.async_payment_failed")
      handle_checkout_session_async_payment_failed
    when "customer.subscription.updated"
      Rails.logger.info("🔄 Processing customer.subscription.updated")
      handle_subscription_updated
    when "customer.subscription.deleted"
      Rails.logger.info("🗑️ Processing customer.subscription.deleted")
      handle_subscription_deleted
    when "invoice.payment_succeeded"
      Rails.logger.info("💰 Processing invoice.payment_succeeded")
      handle_invoice_payment_succeeded
    when "invoice.payment_failed"
      Rails.logger.info("❌ Processing invoice.payment_failed")
      handle_invoice_payment_failed
    else
      Rails.logger.info("❓ Unhandled webhook event: #{@event.type}")
      { success: true }
    end
  end

  # Occurs when a Checkout Session has been successfully completed.
  def handle_checkout_session_completed
    session = @event.data.object
    Rails.logger.info("🔍 Looking for order with session ID: #{session.id}")

    order = find_order_by_session_id(session.id)

    unless order
      Rails.logger.error("❌ Order not found for session ID: #{session.id}")
      return { success: false, error: "Order not found" }
    end

    Rails.logger.info("✅ Found order: #{order.id} (status: #{order.status})")

    if session.mode == "subscription"
      Rails.logger.info("🔄 Handling subscription checkout...")
      # Handle subscription checkout
      subscription = Stripe::Subscription.retrieve(session["subscription"])
      Rails.logger.info("📋 Retrieved Stripe subscription: #{subscription.id}")
      order.handle_intent_succeeded!(stripe_subscription_id: subscription.id)
    else
      Rails.logger.info("🔄 Handling one-time payment...")
      # Handle one-time payment
      order.handle_intent_succeeded!
    end

    Rails.logger.info("✅ Successfully processed checkout.session.completed for order #{order.id}")
    { success: true }
  end

  # Occurs when a payment intent using a delayed payment method finally succeeds.
  def handle_checkout_session_async_payment_succeeded
    session = @event.data.object
    Rails.logger.info("🔍 Looking for order with session ID: #{session.id}")

    order = find_order_by_session_id(session.id)

    unless order
      Rails.logger.error("❌ Order not found for session ID: #{session.id}")
      return { success: false, error: "Order not found" }
    end

    Rails.logger.info("✅ Found order: #{order.id} (status: #{order.status})")

    if session.mode == "subscription"
      Rails.logger.info("🔄 Handling async subscription payment...")
      subscription = Stripe::Subscription.retrieve(session["subscription"])
      Rails.logger.info("📋 Retrieved Stripe subscription: #{subscription.id}")
      order.handle_intent_succeeded!(stripe_subscription_id: subscription.id)
    else
      Rails.logger.info("🔄 Handling async one-time payment...")
      order.handle_intent_succeeded!
    end

    Rails.logger.info("✅ Successfully processed checkout.session.async_payment_succeeded for order #{order.id}")
    { success: true }
  end

  # Occurs when a Checkout Session is expired.
  def handle_checkout_session_expired
    session = @event.data.object
    Rails.logger.info("🔍 Looking for order with session ID: #{session.id}")

    order = find_order_by_session_id(session.id)

    unless order
      Rails.logger.error("❌ Order not found for session ID: #{session.id}")
      return { success: false, error: "Order not found" }
    end

    Rails.logger.info("✅ Found order: #{order.id} (status: #{order.status})")
    order.handle_intent_failed!
    Rails.logger.info("✅ Successfully processed checkout.session.expired for order #{order.id}")
    { success: true }
  end

  # Occurs when a payment intent using a delayed payment method fails.
  def handle_checkout_session_async_payment_failed
    session = @event.data.object
    Rails.logger.info("🔍 Looking for order with session ID: #{session.id}")

    order = find_order_by_session_id(session.id)

    unless order
      Rails.logger.error("❌ Order not found for session ID: #{session.id}")
      return { success: false, error: "Order not found" }
    end

    Rails.logger.info("✅ Found order: #{order.id} (status: #{order.status})")
    order.handle_intent_failed!
    Rails.logger.info("✅ Successfully processed checkout.session.async_payment_failed for order #{order.id}")
    { success: true }
  end

  # Occurs when a subscription is updated (status change, plan change, etc.)
  def handle_subscription_updated
    subscription_data = @event.data.object
    Rails.logger.info("🔍 Processing subscription update for: #{subscription_data.id}")

    # Delegate to SubscriptionWebhookService for detailed processing
    result = SubscriptionWebhookService.new(@event).handle_subscription_updated

    Rails.logger.info("✅ Successfully processed customer.subscription.updated for subscription #{subscription_data.id}")
    result
  end

  # Occurs when a subscription is deleted/cancelled
  def handle_subscription_deleted
    subscription_data = @event.data.object
    Rails.logger.info("🔍 Processing subscription deletion for: #{subscription_data.id}")

    # Delegate to SubscriptionWebhookService for detailed processing
    result = SubscriptionWebhookService.new(@event).handle_subscription_deleted

    Rails.logger.info("✅ Successfully processed customer.subscription.deleted for subscription #{subscription_data.id}")
    result
  end

  # Occurs when an invoice payment succeeds (subscription renewal)
  def handle_invoice_payment_succeeded
    invoice_data = @event.data.object
    Rails.logger.info("🔍 Processing successful invoice payment for: #{invoice_data.id}")

    # Check if this is a subscription invoice
    # For subscription invoices, the subscription ID can be in different places:
    # 1. Direct field: invoice_data["subscription"]
    # 2. Parent details: invoice_data["parent"]["subscription_details"]["subscription"]
    subscription_id = invoice_data["subscription"] ||
                     invoice_data.dig("parent", "subscription_details", "subscription")

    if subscription_id
      Rails.logger.info("📋 Found subscription ID: #{subscription_id} for invoice #{invoice_data.id}")
      # Delegate to SubscriptionWebhookService for detailed processing
      result = SubscriptionWebhookService.new(@event).handle_invoice_payment_succeeded

      Rails.logger.info("✅ Successfully processed invoice.payment_succeeded for invoice #{invoice_data.id}")
      result
    else
      Rails.logger.info("ℹ️ Skipping non-subscription invoice: #{invoice_data.id}")
      { success: true }
    end
  end

  # Occurs when an invoice payment fails (subscription renewal failure)
  def handle_invoice_payment_failed
    invoice_data = @event.data.object
    Rails.logger.info("🔍 Processing failed invoice payment for: #{invoice_data.id}")

    # Check if this is a subscription invoice
    # For subscription invoices, the subscription ID can be in different places:
    # 1. Direct field: invoice_data["subscription"]
    # 2. Parent details: invoice_data["parent"]["subscription_details"]["subscription"]
    subscription_id = invoice_data["subscription"] ||
                     invoice_data.dig("parent", "subscription_details", "subscription")

    if subscription_id
      Rails.logger.info("📋 Found subscription ID: #{subscription_id} for invoice #{invoice_data.id}")
      # Delegate to SubscriptionWebhookService for detailed processing
      result = SubscriptionWebhookService.new(@event).handle_invoice_payment_failed

      Rails.logger.info("✅ Successfully processed invoice.payment_failed for invoice #{invoice_data.id}")
      result
    else
      Rails.logger.info("ℹ️ Skipping non-subscription invoice: #{invoice_data.id}")
      { success: true }
    end
  end

  def find_order_by_session_id(session_id)
    Rails.logger.debug("🔍 Searching for order with session_id: #{session_id}")
    order = Order.find_by(stripe_session_id: session_id)
    if order
      Rails.logger.debug("✅ Found order: #{order.id}")
    else
      Rails.logger.debug("❌ No order found with session_id: #{session_id}")
    end
    order
  end
end
