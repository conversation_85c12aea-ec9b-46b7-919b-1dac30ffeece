<div class="space-y-6">
  <div class="text-center">
    <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-lg dark:bg-blue-900">
      <svg class="size-6 text-blue-600 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
        <path fill-rule="evenodd" d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4h-4Z" clip-rule="evenodd"/>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Check your email</h3>
    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
      We've sent a verification code to <strong class="text-gray-900 dark:text-white"><%= email %></strong>
      <br>
      This code will expire in <%= pluralize(OtpService::EXPIRATION_TIME.in_minutes.to_i, "minute") %>.
    </p>
  </div>
  <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800" role="alert">
    <div class="flex items-center">
      <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
      </svg>
      <span class="sr-only">Info</span>
      <div>
        <span class="font-medium">Check your spam folder</span> if you don't see the email in your inbox.
      </div>
    </div>
  </div>
  <%= form_with url: verify_otp_sessions_path, method: :post, local: false, class: "space-y-6" do |form| %>
    <%= form.hidden_field :email, value: email %>
    <div>
      <%= form.label :otp_pin, "Verification Code", class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3.5 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
          </svg>
        </div>
        <%= form.text_field :otp_pin, required: true, autofocus: true, maxlength: 6, minlength: 6,
            class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 text-center tracking-widest dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",
            placeholder: "000000",
            pattern: "[0-9]{6}",
            inputmode: "numeric" %>
      </div>
      <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Enter the 6-digit code sent to your email</p>
    </div>
    <div>
      <%= form.submit "Verify and Sign In", 
          class: "w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" %>
    </div>
  <% end %>
  <div class="text-center">
    <%= link_to "Back to email", login_path, 
        class: "inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-500 dark:hover:text-blue-700" %>
  </div>
</div>