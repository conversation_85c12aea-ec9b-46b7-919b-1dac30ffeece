<div data-controller="dropdown">
  <button type="button" 
    data-action="click->dropdown#toggle"
    data-dropdown-target="button"
    class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
    <%= flowbite_icon('filter-outline', class: 'w-4 h-4 mr-2 text-gray-400') %>
    Filter
    <%= flowbite_icon('chevron-down-outline', class: 'w-4 h-4 ml-1') %>
  </button>

  <div data-dropdown-target="menu" 
    class="z-10 hidden absolute right-0 mt-2 w-64 p-3 bg-white rounded-lg shadow">
    
    <!-- Favorites Filter -->
    <div class="mb-4">
      <h6 class="mb-2 text-sm font-medium text-gray-900">Show</h6>
      <div class="space-y-2">
        <div class="flex items-center">
          <%= link_to songs_path(params.except(:favorites).merge(page: 1)), 
            data: { turbo_frame: "song_list_frame" },
            class: "flex items-center w-full text-sm #{'text-blue-600 font-medium' if params[:favorites] != 'true'}" do %>
            <div class="w-4 h-4 mr-2 border border-gray-300 rounded #{'bg-blue-600 border-blue-600' if params[:favorites] != 'true'}">
              <% if params[:favorites] != 'true' %>
                <%= flowbite_icon('check-outline', class: 'w-3 h-3 text-white') %>
              <% end %>
            </div>
            All Songs
          <% end %>
        </div>
        <div class="flex items-center">
          <%= link_to songs_path(params.merge(favorites: 'true', page: 1)), 
            data: { turbo_frame: "song_list_frame" },
            class: "flex items-center w-full text-sm #{'text-blue-600 font-medium' if params[:favorites] == 'true'}" do %>
            <div class="w-4 h-4 mr-2 border border-gray-300 rounded #{'bg-blue-600 border-blue-600' if params[:favorites] == 'true'}">
              <% if params[:favorites] == 'true' %>
                <%= flowbite_icon('check-outline', class: 'w-3 h-3 text-white') %>
              <% end %>
            </div>
            Favorites Only
          <% end %>
        </div>
      </div>
    </div>

    <!-- Genre Filter -->
    <div class="mb-4">
      <h6 class="mb-2 text-sm font-medium text-gray-900">Genre</h6>
      <%= form_with url: songs_path, method: :get, local: false, data: { turbo_frame: "song_list_frame" } do |form| %>
        <%= form.text_field :genre, 
          value: params[:genre],
          placeholder: "Enter genre...",
          class: "w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",
          onchange: "this.form.submit()" %>
        <!-- Preserve other parameters -->
        <%= form.hidden_field :search, value: params[:search] %>
        <%= form.hidden_field :sort, value: params[:sort] %>
        <%= form.hidden_field :order, value: params[:order] %>
        <%= form.hidden_field :favorites, value: params[:favorites] %>
        <%= form.hidden_field :style, value: params[:style] %>
        <%= form.hidden_field :page, value: 1 %>
      <% end %>
    </div>

    <!-- Style Filter -->
    <div class="mb-4">
      <h6 class="mb-2 text-sm font-medium text-gray-900">Style</h6>
      <%= form_with url: songs_path, method: :get, local: false, data: { turbo_frame: "song_list_frame" } do |form| %>
        <%= form.text_field :style, 
          value: params[:style],
          placeholder: "Enter style...",
          class: "w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",
          onchange: "this.form.submit()" %>
        <!-- Preserve other parameters -->
        <%= form.hidden_field :search, value: params[:search] %>
        <%= form.hidden_field :sort, value: params[:sort] %>
        <%= form.hidden_field :order, value: params[:order] %>
        <%= form.hidden_field :favorites, value: params[:favorites] %>
        <%= form.hidden_field :genre, value: params[:genre] %>
        <%= form.hidden_field :page, value: 1 %>
      <% end %>
    </div>

    <!-- Clear Filters -->
    <div class="pt-2 border-t border-gray-200">
      <%= link_to "Clear All Filters", songs_path, 
        data: { turbo_frame: "song_list_frame" },
        class: "block w-full px-3 py-2 text-sm text-center text-gray-700 hover:bg-gray-100 rounded-lg" %>
    </div>
  </div>
</div>
