<% if @total_pages > 1 %>
  <div class="flex items-center justify-between p-4 border-t border-gray-200">
    <!-- Results Info -->
    <span class="text-sm text-gray-500">
      Showing 
      <span class="font-medium"><%= (@page - 1) * @per_page + 1 %></span>-<span class="font-medium"><%= [@page * @per_page, @total_count].min %></span> 
      of 
      <span class="font-medium"><%= @total_count %></span> 
      <%= @total_count == 1 ? 'song' : 'songs' %>
    </span>

    <!-- Pagination Controls -->
    <div class="flex items-center space-x-2">
      <!-- Previous Button -->
      <% if @page > 1 %>
        <%= link_to songs_path(params.merge(page: @page - 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "flex items-center justify-center px-3 py-1.5 text-sm text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700" do %>
          <%= flowbite_icon('chevron-left-outline', class: 'w-4 h-4 mr-1') %>
          Previous
        <% end %>
      <% else %>
        <span class="flex items-center justify-center px-3 py-1.5 text-sm text-gray-400 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
          <%= flowbite_icon('chevron-left-outline', class: 'w-4 h-4 mr-1') %>
          Previous
        </span>
      <% end %>

      <!-- Page Info -->
      <span class="px-3 py-1.5 text-sm text-gray-700">
        Page <%= @page %> of <%= @total_pages %>
      </span>

      <!-- Next Button -->
      <% if @page < @total_pages %>
        <%= link_to songs_path(params.merge(page: @page + 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "flex items-center justify-center px-3 py-1.5 text-sm text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700" do %>
          Next
          <%= flowbite_icon('chevron-right-outline', class: 'w-4 h-4 ml-1') %>
        <% end %>
      <% else %>
        <span class="flex items-center justify-center px-3 py-1.5 text-sm text-gray-400 bg-gray-100 border border-gray-200 rounded-lg cursor-not-allowed">
          Next
          <%= flowbite_icon('chevron-right-outline', class: 'w-4 h-4 ml-1') %>
        </span>
      <% end %>
    </div>
  </div>
<% else %>
  <!-- Show results info even when there's only one page -->
  <div class="p-4 border-t border-gray-200">
    <span class="text-sm text-gray-500">
      <% if @total_count > 0 %>
        Showing all <%= @total_count %> <%= @total_count == 1 ? 'song' : 'songs' %>
      <% else %>
        No songs found
      <% end %>
    </span>
  </div>
<% end %>
