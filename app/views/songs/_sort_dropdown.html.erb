<div data-controller="dropdown">
  <button type="button" 
    data-action="click->dropdown#toggle"
    data-dropdown-target="button"
    class="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg md:w-auto focus:outline-none hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
    <%= flowbite_icon('sort-outline', class: 'w-4 h-4 mr-2 text-gray-400') %>
    Sort
    <%= flowbite_icon('chevron-down-outline', class: 'w-4 h-4 ml-1') %>
  </button>

  <div data-dropdown-target="menu" 
    class="z-10 hidden absolute right-0 mt-2 w-48 bg-white divide-y divide-gray-100 rounded-lg shadow">
    <ul class="py-2 text-sm text-gray-700">
      <li>
        <%= link_to songs_path(params.merge(sort: 'default', order: 'desc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if (params[:sort].blank? || params[:sort] == 'default')}" do %>
          <%= flowbite_icon('clock-outline', class: 'w-4 h-4 inline mr-2') %>
          Default (Generation Priority)
        <% end %>
      </li>
      <li>
        <%= link_to songs_path(params.merge(sort: 'created_at', order: 'desc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if params[:sort] == 'created_at' && params[:order] != 'asc'}" do %>
          <%= flowbite_icon('calendar-outline', class: 'w-4 h-4 inline mr-2') %>
          Newest First
        <% end %>
      </li>
      <li>
        <%= link_to songs_path(params.merge(sort: 'created_at', order: 'asc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if params[:sort] == 'created_at' && params[:order] == 'asc'}" do %>
          <%= flowbite_icon('calendar-outline', class: 'w-4 h-4 inline mr-2') %>
          Oldest First
        <% end %>
      </li>
      <li>
        <%= link_to songs_path(params.merge(sort: 'title', order: 'asc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if params[:sort] == 'title' && params[:order] != 'desc'}" do %>
          <%= flowbite_icon('sort-alpha-down-outline', class: 'w-4 h-4 inline mr-2') %>
          Title A-Z
        <% end %>
      </li>
      <li>
        <%= link_to songs_path(params.merge(sort: 'title', order: 'desc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if params[:sort] == 'title' && params[:order] == 'desc'}" do %>
          <%= flowbite_icon('sort-alpha-up-outline', class: 'w-4 h-4 inline mr-2') %>
          Title Z-A
        <% end %>
      </li>
      <li>
        <%= link_to songs_path(params.merge(sort: 'favorite', order: 'desc', page: 1)), 
          data: { turbo_frame: "song_list_frame" },
          class: "block px-4 py-2 hover:bg-gray-100 #{'bg-gray-50' if params[:sort] == 'favorite'}" do %>
          <%= flowbite_icon('heart-outline', class: 'w-4 h-4 inline mr-2') %>
          Favorites First
        <% end %>
      </li>
    </ul>
  </div>
</div>
