<% content_for :title, "My Songs" %>
<%# Subscribe to Turbo Streams for generation task updates %>
<%= turbo_stream_from Current.user, :generation_task_updates %>
<div class="min-h-screen bg-gray-50 flex flex-col pb-24">
  <!-- Header Section -->
  <div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">My Songs</h1>
          <p class="text-gray-600 mt-1">Generate, manage, and explore your AI-created music</p>
        </div>
      </div>
    </div>
  </div>
  <!-- Three-Column Layout with bottom padding for floating player -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 flex-1">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-1">
      <!-- Left Column: Generation Form -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex flex-col">
          <div class="p-4 border-b border-gray-200 flex-shrink-0">
            <h2 class="text-lg font-semibold text-gray-900">Generate Music</h2>
            <p class="text-sm text-gray-600 mt-1">Create new AI-powered music</p>
          </div>
          <%= turbo_frame_tag "generation_form", src: new_generation_path, class: "flex-1 flex flex-col" do %>
            <div class="p-6 text-center flex-1 flex flex-col justify-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p class="text-gray-500 mt-2">Loading generation form...</p>
            </div>
          <% end %>
        </div>
      </div>
      <!-- Middle Column: Song List -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex flex-col">
          <div class="p-4 border-b border-gray-200 flex-shrink-0">
            <h2 class="text-lg font-semibold text-gray-900">My Songs</h2>
            <%= render "songs/song_stats" %>
          </div>
          <!-- Search and Filter Controls -->
          <%= render "search_controls" %>
          <!-- Song List with Turbo Frame -->
          <%= turbo_frame_tag "song_list_frame", class: "flex-1 flex flex-col" do %>
            <div class="flex-1 overflow-y-auto flex flex-col">
              <%= render "song_list", songs: @songs, generating_tasks: @generating_tasks, failed_tasks: @failed_tasks %>
            </div>
            <!-- Pagination -->
            <%= render "simple_pagination" %>
          <% end %>
        </div>
      </div>
      <!-- Right Column: Song Details -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex flex-col">
          <div class="p-4 border-b border-gray-200 flex-shrink-0">
            <h2 class="text-lg font-semibold text-gray-900">Song Details</h2>
            <p class="text-sm text-gray-600 mt-1">Select a song to view details</p>
          </div>
          <%= turbo_frame_tag "song_details", class: "flex-1 flex flex-col" do %>
            <%= render "empty_song" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <!-- Persistent/Floating Media Player -->
  <div class="fixed bottom-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out">
    <!-- Main player container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Player component -->
      <%= turbo_frame_tag "song_player", class: "mt-2" do %>
        <%= render "player" %>
      <% end %>
    </div>
  </div>
</div>