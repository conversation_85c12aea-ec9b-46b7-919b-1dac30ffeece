class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.string :username, null: false
      t.integer :extra_credits, default: 0, null: false
      t.integer :plan_credits_used, default: 0, null: false
      t.integer :plan_credits_limit, default: 0, null: false

      t.timestamps
    end
    # Add a case-insensitive unique index using LOWER function
    # This ensures uniqueness regardless of case while maintaining performance
    add_index :users, "LOWER(username)", unique: true, name: "index_users_on_lower_username"

    # Also add a regular index for case-sensitive lookups (for performance)
    add_index :users, :username, name: "index_users_on_username"
  end
end
