class AddSearchIndexesToSongs < ActiveRecord::Migration[8.0]
  def change
    # Add GIN index for metadata JSONB column to improve search performance
    add_index :songs, :metadata, using: :gin
    
    # Add specific indexes for commonly searched metadata fields
    add_index :songs, "(metadata->>'title')", name: 'index_songs_on_metadata_title'
    add_index :songs, "(metadata->>'artist')", name: 'index_songs_on_metadata_artist'
    add_index :songs, "(metadata->>'genre')", name: 'index_songs_on_metadata_genre'
    add_index :songs, "(metadata->>'style')", name: 'index_songs_on_metadata_style'
    
    # Composite index for user_id and favorite for filtering
    add_index :songs, [:user_id, :favorite]
    
    # Composite index for user_id and created_at for sorting
    add_index :songs, [:user_id, :created_at]
  end
end
